import { gql } from '@apollo/client';
import { USER_FRAGMENT, COMMENT_FRAGMENT, USER_DETAIL_FRAGMENT, NOTIFICATION_FRAGMENT } from './fragments';

export const REACTION_MUTATION = gql`
  mutation Reaction($type: ReactionTypeEnum!, $postId: ID!) {
    addReaction(input: { type: $type, postId: $postId }) {
      success
    }
  }
`;

export const REMOVE_REACTION_MUTATION = gql`
  mutation RemoveReaction($type: ReactionTypeEnum!, $postId: ID!) {
    removeReaction(input: { type: $type, postId: $postId }) {
      success
    }
  }
`;

// 用户注册
export const REGISTER_USER = gql`
  mutation RegisterUser($username: String!, $email: String!, $password: String!, $phone: String) {
    registerUser(
      input: {
        username: $username
        email: $email
        password: $password
        phone: $phone
      }
    ) {
      user {
        ...UserDetailFields
      }
    }
  }
  ${USER_DETAIL_FRAGMENT}
`;

// 用户登录
export const LOGIN_USER = gql`
  mutation LoginUser($input: LoginInput!) {
    login(input: $input) {
      authToken
      refreshToken
      user {
        id
        name
        email
        databaseId
        username
      }
    }
  }
`;

// 更新个人资料
export const UPDATE_PROFILE = gql`
  mutation UpdateProfile($id: ID!, $firstName: String, $lastName: String, $email: String, $description: String, $nickname: String) {
    updateUser(
      input: {
        id: $id
        firstName: $firstName
        lastName: $lastName
        email: $email
        description: $description
        nickname: $nickname
      }
    ) {
      user {
        ...UserDetailFields
      }
    }
  }
  ${USER_DETAIL_FRAGMENT}
`;

// 发送密码重置邮件
export const SEND_PASSWORD_RESET_EMAIL = gql`
  mutation SendPasswordResetEmail($username: String!) {
    sendPasswordResetEmail(
      input: {
        username: $username
      }
    ) {
      user {
        id
        email
      }
      success
    }
  }
`;

// 重置密码
export const RESET_USER_PASSWORD = gql`
  mutation ResetUserPassword($key: String!, $login: String!, $password: String!) {
    resetUserPassword(
      input: {
        key: $key
        login: $login
        password: $password
      }
    ) {
      user {
        ...UserDetailFields
      }
      success
    }
  }
  ${USER_DETAIL_FRAGMENT}
`;

// 更改密码
export const CHANGE_PASSWORD = gql`
  mutation ChangePassword($currentPassword: String!, $newPassword: String!) {
    changePassword(
      input: {
        currentPassword: $currentPassword
        newPassword: $newPassword
      }
    ) {
      success
      message
    }
  }
`;

// 创建评论
export const CREATE_COMMENT = gql`
  mutation CreateComment($input: CreateCommentInput!) {
    createComment(input: $input) {
      success
      comment {
        ...CommentFields
      }
    }
  }
  ${COMMENT_FRAGMENT}
`;

// 更新评论
export const UPDATE_COMMENT = gql`
  mutation UpdateComment($input: UpdateCommentInput!) {
    updateComment(input: $input) {
      success
      comment {
        ...CommentFields
      }
    }
  }
  ${COMMENT_FRAGMENT}
`;

// 删除评论
export const DELETE_COMMENT = gql`
  mutation DeleteComment($id: ID!) {
    deleteComment(input: { id: $id }) {
      success
      comment {
        ...CommentFields
      }
    }
  }
  ${COMMENT_FRAGMENT}
`;

// 恢复评论
export const RESTORE_COMMENT = gql`
  mutation RestoreComment($input: RestoreCommentInput!) {
    restoreComment(input: $input) {
      comment {
        ...CommentFields
      }
    }
  }
  ${COMMENT_FRAGMENT}
`;

// 更新评论状态
export const UPDATE_COMMENT_STATUS = gql`
  mutation UpdateCommentStatus($id: ID!, $status: CommentStatusEnum!) {
    updateComment(input: {
      id: $id
      status: $status
    }) {
      comment {
        ...CommentFields
      }
    }
  }
  ${COMMENT_FRAGMENT}
`;

// 添加文章到收藏夹
export const ADD_TO_FAVORITES = gql`
  mutation AddToFavorites($userId: ID!, $postId: ID!) {
    addToFavorites(input: { userId: $userId, postId: $postId }) {
      success
      userId
      postId
    }
  }
`;

// 从收藏夹移除文章
export const REMOVE_FROM_FAVORITES = gql`
  mutation RemoveFromFavorites($userId: ID!, $postId: ID!) {
    removeFromFavorites(input: { userId: $userId, postId: $postId }) {
      success
      userId
      postId
    }
  }
`;

// 发送密码重置验证码
export const SEND_PASSWORD_RESET_CODE = gql`
  mutation SendPasswordResetCode($email: String!) {
    sendPasswordResetCode(
      input: {
        email: $email
      }
    ) {
      success
      message
    }
  }
`;

// 通过验证码重置密码
export const RESET_PASSWORD_WITH_CODE = gql`
  mutation ResetPasswordWithCode($email: String!, $code: String!, $newPassword: String!) {
    resetPasswordWithCode(
      input: {
        email: $email
        code: $code
        newPassword: $newPassword
      }
    ) {
      success
      message
    }
  }
`;

// 通过手机验证码重置密码
export const RESET_PASSWORD_WITH_PHONE_CODE = gql`
  mutation ResetPasswordWithPhoneCode($phone: String!, $code: String!, $newPassword: String!, $nationCode: String) {
    resetPasswordWithPhoneCode(
      input: {
        phone: $phone
        code: $code
        newPassword: $newPassword
        nationCode: $nationCode
      }
    ) {
      success
      message
    }
  }
`;

// 验证重置密码验证码
export const VERIFY_RESET_CODE = gql`
  mutation VerifyResetCode($email: String!, $code: String!) {
    verifyResetCode(
      input: {
        email: $email
        code: $code
      }
    ) {
      success
      message
      isValid
    }
  }
`;

// 更新用户头像
export const UPDATE_AVATAR = gql`
  mutation UpdateAvatar($mediaId: ID, $base64Image: String) {
    updateAvatar(
      input: {
        mediaId: $mediaId
        base64Image: $base64Image
      }
    ) {
      success
      message
      avatarUrl
      user {
        ...UserDetailFields
      }
    }
  }
  ${USER_DETAIL_FRAGMENT}
`;

// 发送手机验证码
export const SEND_PHONE_CODE = gql`
  mutation SendPhoneCode($phone: String!, $nationCode: String) {
    sendPhoneCode(input: {
      phone: $phone
      nationCode: $nationCode
    }) {
      success
      message
    }
  }
`;

// 手机号登录
export const PHONE_LOGIN = gql`
  mutation PhoneLogin($phone: String!, $code: String!, $nationCode: String) {
    phoneLogin(input: {
      phone: $phone
      code: $code
      nationCode: $nationCode
    }) {
      success
      message
      user {
        ...UserDetailFields
      }
      authToken
    }
  }
  ${USER_DETAIL_FRAGMENT}
`;

// 手机号注册
export const PHONE_REGISTER = gql`
  mutation PhoneRegister($phone: String!, $code: String!, $nationCode: String, $username: String, $displayName: String, $password: String) {
    phoneRegister(input: {
      phone: $phone
      code: $code
      nationCode: $nationCode
      username: $username
      displayName: $displayName
      password: $password
    }) {
      success
      message
      user {
        ...UserDetailFields
      }
      authToken
    }
  }
  ${USER_DETAIL_FRAGMENT}
`;

// 验证手机验证码并获取绑定用的令牌
export const VERIFY_PHONE_CODE_FOR_BINDING_AND_GET_TOKEN = gql`
  mutation VerifyPhoneCodeForBindingAndGetToken($phone: String!, $code: String!, $nationCode: String) {
    verifyPhoneCodeForBindingAndGetToken(
      input: {
        phone: $phone
        code: $code
        nationCode: $nationCode
      }
    ) {
      success
      message
      token
    }
  }
`;

// 绑定手机号
export const BIND_PHONE = gql`
  mutation BindPhone($phone: String!, $token: String!, $nationCode: String) {
    bindPhone(input: {
      phone: $phone
      token: $token
      nationCode: $nationCode
    }) {
      success
      message
      user {
        ...UserDetailFields
      }
    }
  }
  ${USER_DETAIL_FRAGMENT}
`;

// 验证手机验证码
export const VERIFY_PHONE_CODE = gql`
  mutation VerifyPhoneCode($phone: String!, $code: String!, $nationCode: String) {
    verifyPhoneCode(
      input: {
        phone: $phone
        code: $code
        nationCode: $nationCode
      }
    ) {
      success
      message
    }
  }
`;

// 解绑手机号
export const UNBIND_PHONE = gql`
  mutation UnbindPhone {
    unbindPhone(input: {}) {
      success
      message
      user {
        ...UserDetailFields
      }
    }
  }
  ${USER_DETAIL_FRAGMENT}
`;

// 发送通用验证码
export const SEND_VERIFICATION_CODE = gql`
  mutation SendVerificationCode($email: String!, $type: String, $templateData: String) {
    sendVerificationCode(
      input: {
        email: $email
        type: $type
        templateData: $templateData
      }
    ) {
      success
      message
    }
  }
`;

// 验证通用验证码
export const VERIFY_VERIFICATION_CODE = gql`
  mutation VerifyVerificationCode($email: String!, $code: String!, $type: String, $autoClear: Boolean) {
    verifyVerificationCode(
      input: {
        email: $email
        code: $code
        type: $type
        autoClear: $autoClear
      }
    ) {
      success
      isValid
      message
      userId
    }
  }
`;

// 发送注册验证码
export const SEND_REGISTRATION_CODE = gql`
  mutation SendRegistrationCode($email: String!) {
    sendRegistrationCode(
      input: {
        email: $email
      }
    ) {
      success
      message
    }
  }
`;

// 验证注册验证码
export const VERIFY_REGISTRATION_CODE = gql`
  mutation VerifyRegistrationCode($email: String!, $code: String!, $autoClear: Boolean) {
    verifyRegistrationCode(
      input: {
        email: $email
        code: $code
        autoClear: $autoClear
      }
    ) {
      success
      isValid
      message
    }
  }
`;

// 验证注册邮箱验证码并获取令牌的mutation
export const VERIFY_REGISTRATION_CODE_AND_GET_TOKEN = gql`
  mutation VerifyRegistrationCodeAndGetToken($email: String!, $code: String!) {
    verifyRegistrationCodeAndGetToken(input: { email: $email, code: $code }) {
      success
      message
      token
    }
  }
`;

// 使用验证令牌进行注册的mutation
export const REGISTER_WITH_TOKEN = gql`
  mutation RegisterWithToken(
    $username: String!,
    $email: String!,
    $password: String!,
    $token: String!
  ) {
    registerWithToken(input: {
      username: $username,
      email: $email,
      password: $password,
      token: $token
    }) {
      success
      message
      userId
    }
  }
`;

// 验证手机验证码并获取令牌
export const VERIFY_PHONE_CODE_AND_GET_TOKEN = gql`
  mutation VerifyPhoneCodeAndGetToken($phone: String!, $code: String!, $nationCode: String) {
    verifyPhoneCodeAndGetToken(
      input: {
        phone: $phone
        code: $code
        nationCode: $nationCode
      }
    ) {
      success
      message
      token
    }
  }
`;

// 使用验证令牌进行手机注册
export const PHONE_REGISTER_WITH_TOKEN = gql`
  mutation PhoneRegisterWithToken(
    $phone: String!,
    $username: String,
    $password: String,
    $displayName: String,
    $token: String!,
    $nationCode: String
  ) {
    phoneRegisterWithToken(input: {
      phone: $phone,
      username: $username,
      password: $password,
      displayName: $displayName,
      token: $token,
      nationCode: $nationCode
    }) {
      success
      message
      userId
    }
  }
`;

// 使用验证令牌进行手机登录
export const PHONE_LOGIN_WITH_TOKEN = gql`
  mutation PhoneLoginWithToken(
    $phone: String!,
    $token: String!,
    $nationCode: String
  ) {
    phoneLoginWithToken(input: {
      phone: $phone,
      token: $token,
      nationCode: $nationCode
    }) {
      success
      message
      user {
        ...UserDetailFields
      }
      authToken
    }
  }
  ${USER_DETAIL_FRAGMENT}
`;

// 发送邮箱绑定验证码
export const SEND_EMAIL_BINDING_CODE = gql`
  mutation SendEmailBindingCode($email: String!) {
    sendEmailBindingCode(input: {
      email: $email
    }) {
      success
      message
    }
  }
`;

// 验证邮箱绑定验证码并获取令牌
export const VERIFY_EMAIL_BINDING_CODE_AND_GET_TOKEN = gql`
  mutation VerifyEmailBindingCodeAndGetToken($email: String!, $code: String!) {
    verifyEmailBindingCodeAndGetToken(input: {
      email: $email,
      code: $code
    }) {
      success
      message
      token
    }
  }
`;

// 绑定邮箱
export const BIND_EMAIL = gql`
  mutation BindEmail($email: String!, $token: String!) {
    bindEmail(input: {
      email: $email,
      token: $token
    }) {
      success
      message
      user {
        ...UserDetailFields
      }
    }
  }
  ${USER_DETAIL_FRAGMENT}
`;

// 创建支付订单
export const CREATE_ORDER = gql`
  mutation CreateOrder($input: CreateOrderInput!) {
    createOrder(input: $input) {
      status
      message
      order {
        id
        orderNumber
        title
        amount
        paymentMethod
        paymentStatus
        createdAt
      }
      paymentUrl
    }
  }
`;

// 获取支付链接
export const GET_PAYMENT_URL = gql`
  mutation GetPaymentUrl($input: GetPaymentUrlInput!) {
    getPaymentUrl(input: $input) {
      status
      message
      paymentUrl
      paymentHtml
      qrcodeUrl
      isMobile
      isWeixin
    }
  }
`;

// 检查支付状态
export const CHECK_PAYMENT_STATUS = gql`
  mutation CheckPaymentStatus($input: CheckPaymentStatusInput!) {
    checkPaymentStatus(input: $input) {
      status
      paymentStatus
      isPaid
    }
  }
`;

// 余额支付
export const PAY_WITH_BALANCE = gql`
  mutation PayWithBalance($input: PayWithBalanceInput!) {
    payWithBalance(input: $input) {
      status
      message
      orderId
      newBalance
      orderNumber
    }
  }
`;

// 使用余额支付已有订单
export const PAY_ORDER_WITH_BALANCE = gql`
  mutation PayOrderWithBalance($input: PayOrderWithBalanceInput!) {
    payOrderWithBalance(input: $input) {
      status
      message
      orderId
      newBalance
      orderNumber
    }
  }
`;

// 积分支付
export const PAY_WITH_POINTS = gql`
  mutation PayWithPoints($input: PayWithPointsInput!) {
    payWithPoints(input: $input) {
      status
      message
      orderId
      newPoints
      orderNumber
    }
  }
`;

// 使用积分支付已有订单
export const PAY_ORDER_WITH_POINTS = gql`
  mutation PayOrderWithPoints($input: PayOrderWithPointsInput!) {
    payOrderWithPoints(input: $input) {
      status
      message
      orderId
      newPoints
      orderNumber
    }
  }
`;

// 创建会员升级订单
export const CREATE_MEMBER_PAYMENT_ORDER = gql`
  mutation CreateMemberPaymentOrder($levelId: Int!) {
    createMemberPaymentOrder(input: {
      levelId: $levelId
    }) {
      status
      message
      order {
        id
        orderNumber
        title
        amount
        paymentStatus
        createdAt
      }
      paymentUrl
    }
  }
`;

// 标记通知为已读
export const MARK_NOTIFICATION_READ = gql`
  mutation MarkNotificationRead($id: ID!) {
    markNotificationRead(input: { id: $id }) {
      success
      notification {
        ...NotificationFields
      }
    }
  }
  ${NOTIFICATION_FRAGMENT}
`;

// 标记所有通知为已读
export const MARK_ALL_NOTIFICATIONS_READ = gql`
  mutation MarkAllNotificationsRead {
    markAllNotificationsRead(input: {}) {
      success
      count
    }
  }
`;

// 删除通知
export const DELETE_NOTIFICATION = gql`
  mutation DeleteNotification($id: ID!) {
    deleteNotification(input: { id: $id }) {
      success
      deletedId
    }
  }
`;

/**
 * Private Messaging Mutations
 */

export const SEND_PRIVATE_MESSAGE = gql`
  mutation SendMessage($recipientId: ID!, $content: String!) {
    sendPrivateMessage(
      input: {
        recipientId: $recipientId
        content: $content
        clientMutationId: "send-private-message"
      }
    ) {
      success
      sentMessage {
        id
        content
      }
      conversation {
        id
      }
    }
  }
`

export const MARK_CONVERSATION_AS_READ = gql`
  mutation MarkAsRead($conversationId: ID!) {
    markConversationAsRead(
      input: {
        conversationId: $conversationId
        clientMutationId: "mark-conversation-as-read"
      }
    ) {
      success
      conversation {
        id
        unreadCount
      }
    }
  }
`

export const DELETE_CONVERSATION = gql`
  mutation DeleteConversation($conversationId: ID!) {
    deleteConversation(
      input: {
        conversationId: $conversationId
        clientMutationId: "delete-conversation"
      }
    ) {
      success
      deletedConversationId
    }
  }
`

// 创建文章解锁订单
export const CREATE_UNLOCK_ORDER = gql`
  mutation CreateUnlockOrder($postId: ID!, $paymentMethod: String) {
    createUnlockOrder(input: { postId: $postId, paymentMethod: $paymentMethod }) {
      orderId
      success
      message
    }
  }
`; 