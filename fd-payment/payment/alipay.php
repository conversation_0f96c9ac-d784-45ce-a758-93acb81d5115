<?php
namespace FD\Payment\Payments;

defined('ABSPATH') || exit;

class Alipay extends Payment {
    private $_config;
    private $is_f2fpay;
    
    public function __construct() {
        $this->id = 'alipay';
        $this->title = '支付宝';
        $this->icon = plugin_dir_url(dirname(__FILE__)) . 'payment/alipay/alipay.svg';
        $this->color = '#1677ff';
        $this->_config = [];

        add_action('wp_ajax_FD_Alipay_order_status', [$this, 'order_status']);
        add_action('wp_ajax_nopriv_FD_Alipay_order_status', [$this, 'order_status']);

        parent::__construct();
    }

    public function init($index = 0){
        $appid = get_option('fd_payment_alipay_appid');
        $private_key = get_option('fd_payment_alipay_private_key');
        $public_key = get_option('fd_payment_alipay_public_key');
        $f2fpay = get_option('fd_payment_alipay_f2fpay');
        $this->is_f2fpay = $f2fpay && is_array($f2fpay) && isset($f2fpay[$index]) && $f2fpay[$index] == '1';
        $this->modal = $this->is_f2fpay; // 当面付使用弹框
        $this->_config = [
            'app_id' => $appid && is_array($appid) && isset($appid[$index]) ? trim($appid[$index]) : '',
            'merchant_private_key' => $private_key && is_array($private_key) && isset($private_key[$index]) ? trim($private_key[$index]) : '',
            'alipay_public_key' => $public_key && is_array($public_key) && isset($public_key[$index]) ? trim($public_key[$index]) : '',
        ];
    }

    public function process_payment($order_id) {
        $url = $this->get_checkout_payment_url($order_id);
        if($this->is_f2fpay){
            return [
                'result' => 'success',
                'redirect' =>  $url,
                'height' => '472px',
                'modal-size' => 'modal-sm'
            ];
        }else{
            return [
                'result' => 'success',
                'redirect' =>  $url
            ];
        }
    }

    private function config($order_id) {
        $config = $this->_config + [
            //异步通知地址
            'notify_url' => $this->get_checkout_notify_url($order_id),
            //同步跳转
            'return_url' => $this->get_checkout_return_url($order_id),
            //编码格式
            'charset' => "UTF-8",
            //签名方式
            'sign_type' => "RSA2",
            //支付宝网关
            'gatewayUrl' => "https://openapi.alipay.com/gateway.do",
            //支付宝公钥,查看地址：https://openhome.alipay.com/platform/keyManage.htm 对应APPID下的支付宝公钥。
        ];
        return $config;
    }

    public function order_status() {
        apply_filters('fd_payment_gateways', []);
        $order_id = isset($_POST['order_id']) && $_POST['order_id'] ? $_POST['order_id'] : '';
        $nonce = isset($_POST['nonce']) && $_POST['nonce'] ? $_POST['nonce'] : '';
        if($this->is_f2fpay && $nonce && wp_verify_nonce($nonce, 'fd_alipay_receipt_page')){
            // 检查订单状态
            $order = fd_payment_get_order($order_id);
            $user_id = get_current_user_id();
            if ($order && $order->payment_status === 'paid') {
                $res = [
                    'redirect' => $this->get_checkout_return_url($order->id),
                    'status' => 'paid'
                ];
                wp_send_json($res);
            }
        }
    }

    public function receipt_page($order_id) {
        // 获取订单信息
        $order = fd_payment_get_order($order_id);
        if (!$order || $order->payment_status === 'paid') return;

        if($this->is_f2fpay){
            include_once dirname(__FILE__) . '/alipay/f2fpay/service/AlipayTradeService.php';
            include_once dirname(__FILE__) . '/alipay/f2fpay/model/builder/AlipayTradePrecreateContentBuilder.php';
            $payRequestBuilder = new \AlipayTradePrecreateContentBuilder();
            self::add_log('#'.$order_id.'发起支付宝支付：当面付', $this->id, $order_id);
        }else if ($this->is_mobile()) {
            include_once dirname(__FILE__) . '/alipay/wappay/service/AlipayTradeService.php';
            include_once dirname(__FILE__) . '/alipay/wappay/buildermodel/AlipayTradeWapPayContentBuilder.php';
            $payRequestBuilder = new \AlipayTradeWapPayContentBuilder();
            self::add_log('#'.$order_id.'发起支付宝支付：手机网站支付', $this->id, $order_id);
        } else {
            include_once dirname(__FILE__) . '/alipay/pagepay/service/AlipayTradeService.php';
            include_once dirname(__FILE__) . '/alipay/pagepay/buildermodel/AlipayTradePagePayContentBuilder.php';
            $payRequestBuilder = new \AlipayTradePagePayContentBuilder();
            self::add_log('#'.$order_id.'发起支付宝支付：电脑网站支付', $this->id, $order_id);
        }

        // 构造参数
        $total = $order->amount;
        $payRequestBuilder->setBody($order->title);
        $payRequestBuilder->setSubject($order->title);
        $payRequestBuilder->setTotalAmount($total);
        $payRequestBuilder->setOutTradeNo($order->order_number);
        
        if ($this->is_mobile() || $this->is_f2fpay) {
            $payRequestBuilder->setTimeExpress('30m');
        }

        $config = $this->config($order_id);
        $aop = new \AlipayTradeService($config);

        if($this->is_f2fpay){
            try{
                $qrPayResult = $aop->qrPay($payRequestBuilder);
                $response = $qrPayResult->getResponse();
                $qrcode = '';
                if($qrPayResult->getTradeStatus() === 'SUCCESS' && isset($response->qr_code) && $response->qr_code){
                    $qrcode = $response->qr_code;
                }else if(isset($response->trade_status) && $response->trade_status == 'TRADE_CLOSED'){
                    // 更新订单号并重新发起支付
                    $response->code = 'TRADE_CLOSED';
                    $response->msg = '订单异常，请关闭订单重新发起支付';
                }
            }catch (\Exception $e){
                $response = new \stdClass;
                $response->code = $e->getCode();
                $response->msg = $e->getMessage();
                
                // 记录错误日志
                self::add_log('#'.$order_id.' 支付宝当面付异常：' . $e->getMessage(), $this->id, $order_id);
            }

            if(isset($qrcode) && $qrcode){ ?>
                <div class="fd-pay-inner">
                    <div id="j-fd-alipay" class="fd-pay-img" data-url="<?php echo $qrcode; ?>" data-order="<?php echo $order_id; ?>"></div>
                    <p class="fd-pay-note" style="line-height:2;font-size: 16px;color: #333;">使用支付宝扫描二维码进行支付</p>
                </div>
                <script src="<?php echo includes_url('js/jquery/jquery.min.js'); ?>"></script>
                <script src="<?php echo plugin_dir_url(dirname(__FILE__)) . 'assets/js/pay.js'; ?>"></script>
                <script> _ajax_url = "<?php echo admin_url( 'admin-ajax.php');?>"; _nonce = "<?php echo wp_create_nonce('fd_alipay_receipt_page');?>"; </script>
            <?php }else{ ?>
                <div class="text-center" style="margin-bottom: 30px;color:#f66;">
                    <span style="color:red;">错误代码:<?php echo $response->code ?>; 错误信息:<?php echo $response->msg; if(isset($response->sub_msg)) echo  '：'. $response->sub_msg ?></span>
                </div>
            <?php }
        }else{ ?>
            <div class="loading-img"></div>
            <p class="loading-text">正在转入支付平台...</p>
            <div class="fd-alipay-html">
                <?php if ($this->is_mobile()) {
                    $aop->wapPay($payRequestBuilder, $config['return_url'], $config['notify_url']);
                } else {
                    $aop->pagePay($payRequestBuilder, $config['return_url'], $config['notify_url']);
                } ?>
            </div>
        <?php }
    }

    public function check_response($order_id) {
        if(!is_numeric($order_id)) return;

        require_once dirname(__FILE__) . '/alipay/pagepay/service/AlipayTradeService.php';

        self::add_log('#支付宝异步通知数据：' . wp_json_encode($_POST), $this->id, $order_id);
        if(!isset($_POST['out_trade_no']))  exit;
        $arr = $_POST;
        $arr['fund_bill_list'] = stripslashes($arr['fund_bill_list']);
        $config = $this->config($order_id);
        $alipaySevice = new \AlipayTradeService($config);
        $result = $alipaySevice->check($arr);

        self::add_log('#'.$arr['out_trade_no'].' 支付宝异步通知：' . $result . '；通知数据：' . wp_json_encode($arr), $this->id, $order_id);
        self::add_log('#'.$arr['out_trade_no'].' 支付宝通知验证：' . ($result && $arr['app_id'] == $config['app_id']), $this->id, $order_id);
        if( $result && $arr['app_id'] == $config['app_id'] ){
            $trade_status = $arr['trade_status'];
            if($trade_status == 'TRADE_FINISHED' || $trade_status == 'TRADE_SUCCESS') {
                self::add_log('#'.$arr['out_trade_no'].' 支付宝异步通知验证成功：' . $trade_status .'；支付宝订单：' . $arr['trade_no'], $this->id, $order_id);
                
                // 获取订单信息并更新状态
                $order = fd_payment_get_order($order_id);
                if($order && $order->order_number && $order->order_number == $arr['out_trade_no'] && $order->payment_status === 'unpaid'){ // 判断订单号，以及支付状态
                    fd_payment_complete_order($order->id, $this->id);
                    self::add_log('#'.$arr['out_trade_no'].' 订单支付完成！', $this->id, $order_id);
                }
            }
            echo "success";
        }else{
            echo "fail";
        }
        exit;
    }

    public static function add_log($log, $type, $order_id = 0) {
        // 调用新的日志系统
        fd_payment_log($log, $type, 'info', $order_id);
    }

    public function return_page($order_id) {
        $order = fd_payment_get_order($order_id);

        $target = isset($this->modal) && $this->modal ? 'target="_parent"' : '';
        if ($order && $order->payment_status === 'paid') {
            // 获取前端域名配置
            $frontend_url = get_option('fd_frontend_url', home_url());
            // 确保URL末尾没有斜杠
            $frontend_url = rtrim($frontend_url, '/');

            // 检查是否是会员等级购买
            if ($order->product_type === 'member_level') {
                // 构建基础重定向URL
                $redirect_url = $frontend_url . '/membership/payment-success';

                // 添加调试日志
                self::add_log('#支付宝支付成功，会员升级订单，订单ID: ' . $order_id, $this->id, $order_id);
                self::add_log('#订单metadata: ' . $order->metadata, $this->id, $order_id);

                // 尝试解析metadata获取returnUrl
                $return_url = null;
                if (!empty($order->metadata)) {
                    $metadata = json_decode($order->metadata, true);
                    if (is_array($metadata) && isset($metadata['returnUrl']) && !empty($metadata['returnUrl'])) {
                        $return_url = $metadata['returnUrl'];
                        self::add_log('#从metadata中提取到returnUrl: ' . $return_url, $this->id, $order_id);
                    }
                }

                // 如果有returnUrl，添加到重定向URL中
                if ($return_url) {
                    $redirect_url .= '?returnUrl=' . urlencode($return_url);
                    self::add_log('#最终重定向URL: ' . $redirect_url, $this->id, $order_id);
                } else {
                    self::add_log('#未找到returnUrl，使用默认重定向URL: ' . $redirect_url, $this->id, $order_id);
                }
                ?>
                <script type="text/javascript">
                    // 使用JavaScript跳转，确保在iframe内也能正确跳转
                    window.top.location.href = "<?php echo esc_url($redirect_url); ?>";
                </script>
                <?php
                // 添加备用HTML跳转
                echo '<div class="order-status order-status-success">';
                echo '<div class="order-status-icon"></div>';
                echo '<p>订单支付成功，正在跳转...</p>';
                echo '<p>如果没有自动跳转，请 <a href="' . esc_url($redirect_url) . '">点击这里</a></p>';
                echo '</div>';
                // 立即结束输出
                exit();
            }

            // 检查是否是文章解锁订单
            if ($order->product_type === 'post_unlock') {
                // 构建文章解锁成功页面URL
                $redirect_url = $frontend_url . '/payment/post-unlock-success';

                // 添加调试日志
                self::add_log('#支付宝支付成功，文章解锁订单，订单ID: ' . $order_id, $this->id, $order_id);
                self::add_log('#订单metadata: ' . $order->metadata, $this->id, $order_id);

                // 尝试解析metadata获取callbackUrl（文章页面URL）
                $callback_url = null;
                if (!empty($order->metadata)) {
                    $metadata = json_decode($order->metadata, true);
                    if (is_array($metadata) && isset($metadata['callbackUrl']) && !empty($metadata['callbackUrl'])) {
                        $callback_url = $metadata['callbackUrl'];
                        self::add_log('#从metadata中提取到callbackUrl: ' . $callback_url, $this->id, $order_id);
                    }
                }

                // 如果有callbackUrl，添加到重定向URL中
                if ($callback_url) {
                    $redirect_url .= '?returnUrl=' . urlencode($callback_url);
                    self::add_log('#最终重定向URL: ' . $redirect_url, $this->id, $order_id);
                } else {
                    self::add_log('#未找到callbackUrl，使用默认重定向URL: ' . $redirect_url, $this->id, $order_id);
                }
                ?>
                <script type="text/javascript">
                    // 使用JavaScript跳转，确保在iframe内也能正确跳转
                    window.top.location.href = "<?php echo esc_url($redirect_url); ?>";
                </script>
                <?php
                // 添加备用HTML跳转
                echo '<div class="order-status order-status-success">';
                echo '<div class="order-status-icon"></div>';
                echo '<p>支付成功！您已解锁文章，正在跳转回到文章页...</p>';
                echo '<p>如果没有自动跳转，请 <a href="' . esc_url($redirect_url) . '">点击这里</a></p>';
                echo '</div>';
                // 立即结束输出
                exit();
            }
            ?>
            <div class="order-status order-status-success">
                <div class="order-status-icon"></div>
                <p>订单支付成功</p>
                <p>订单号：<?php echo esc_html($order->order_number); ?></p>
                <p>商品名称：<?php echo esc_html($order->title); ?></p>
                <p>支付金额：￥<?php echo number_format($order->amount, 2); ?></p>
                <p style="margin: 15px 0;"><a href="<?php echo home_url(); ?>" class="btn">返回网站</a></p>
            </div>
        <?php } else { ?>
            <div class="order-status order-status-fail" style="padding: 30px 15px;">
                <div class="order-status-icon"></div>
                <p>订单暂未完成支付</p>
                <p style="margin-top: 2px;">如有疑问请联系网站客服获取帮助</p>
                <a class="btn btn-help" href="javascript:window.location.reload();">已支付？刷新试试</a>
            </div>
        <?php }
    }
}

new Alipay(); 