<?php
namespace FD\Payment\Payments;

defined('ABSPATH') || exit;

class Wxpay extends Payment {
    private $_config = [];
    
    public function __construct() {
        $this->id = 'wxpay';
        $this->title = '微信支付';
        $this->icon = plugin_dir_url(dirname(__FILE__)) . 'payment/wxpay/wxpay.svg';
        $this->color = '#2bae67';
        $this->modal = !$this->is_mobile(); // 非手机端的时候使用弹框，二维码支付

        add_action('wp_ajax_FD_WXPay_order_status', [$this, 'order_status']);
        add_action('wp_ajax_nopriv_FD_WXPay_order_status', [$this, 'order_status']);

        parent::__construct();
    }

    public function init($index = 0){
        $appid = get_option('fd_payment_wxpay_appid');
        $secret = get_option('fd_payment_wxpay_secret');
        $mchid = get_option('fd_payment_wxpay_mchid');
        $key = get_option('fd_payment_wxpay_key');
        $disable_h5 = get_option('fd_payment_wxpay_disable_h5');
        $this->_config = [
            'appid' => $appid && is_array($appid) && isset($appid[$index]) ? trim($appid[$index]) : '',
            'mchid' => $mchid && is_array($mchid) && isset($mchid[$index]) ? trim($mchid[$index]) : '',
            'key' => $key && is_array($key) && isset($key[$index]) ? trim($key[$index]) : '',
            'secret' => $secret && is_array($secret) && isset($secret[$index]) ? trim($secret[$index]) : '',
            'disable_h5' => $disable_h5 && is_array($disable_h5) && isset($disable_h5[$index]) ? $disable_h5[$index] : '',
        ];
        if ($this->is_mobile() && !$this->is_weixin() && $this->_config['disable_h5']) {
            // 手机端非微信并且没有开启h5支付时使用二维码扫码支付，改为弹框
            $this->modal = true;
        }
    }

    public function process_payment($order_id) {
        $url = $this->get_checkout_payment_url($order_id);
        return [
            'result' => 'success',
            'redirect' =>  $url,
            'height' => '472px',
            'modal-size' => 'modal-sm'
        ];
    }

    public function order_status() {
        apply_filters('fd_payment_gateways', []);
        $order_id = isset($_POST['order_id']) && $_POST['order_id'] ? $_POST['order_id'] : '';
        $nonce = isset($_POST['nonce']) && $_POST['nonce'] ? $_POST['nonce'] : '';

        if($nonce && wp_verify_nonce($nonce, 'fd_wxpay_receipt_page')){
            // 检查订单状态
            $order = fd_payment_get_order($order_id);
            $user_id = get_current_user_id();
            if ($order && $order->payment_status === 'paid') {
                $res = [
                    'redirect' => $this->get_checkout_return_url($order->id),
                    'status' => 'paid'
                ];
                wp_send_json($res);
            }
        }
    }

    public function receipt_page($order_id) {
        // 获取订单信息
        $order = fd_payment_get_order($order_id);
        if (!$order || $order->payment_status === 'paid') return;
        
        require_once dirname(__FILE__) . "/wxpay/WxPay.Config.php";
        $config = new \WeChat_Config($this->_config);
        
        // 手机端、不是微信内打开、未关闭h5支付
        if ($this->is_mobile() && !$this->is_weixin() && !$this->_config['disable_h5']) {
            require_once dirname(__FILE__) . "/wxpay/WxPay.Api.php";
            $trade_type = 'MWEB';
        } else if ($this->is_weixin() && $this->is_mobile() && $this->_config['secret']) { // 使用手机微信内置公众号支付
            include_once dirname(__FILE__) . '/wxpay/WxPay.JsApiPay.php';
            $tools = new \JsApiPay($config);
            $openId = $tools->GetOpenid();
            $trade_type = 'JSAPI';
        } else {
            require_once dirname(__FILE__) . "/wxpay/WxPay.Api.php";
            $trade_type = 'NATIVE';
        }

        self::add_log('#' . $order_id . '发起微信支付：' . $trade_type, $this->id, $order_id);

        $input = new \WxPayUnifiedOrder();
        $input->SetBody($order->title);
        $input->SetOut_trade_no($order->order_number);
        $total = $order->amount;
        $totalFee = (int) ($total * 100);

        $input->SetTotal_fee($totalFee);
        $input->SetFee_type('CNY');

        $startTime = date("YmdHis", current_time('timestamp'));
        $expiredTime = date("YmdHis", current_time('timestamp') + 1800);
        $input->SetTime_start($startTime);
        $input->SetTime_expire($expiredTime);

        $input->SetNotify_url($this->get_checkout_notify_url($order_id));

        $input->SetTrade_type($trade_type);
        if ($trade_type == 'JSAPI') $input->SetOpenid($openId);

        $input->SetProduct_id($order_id);

        try {
            if ($trade_type == 'MWEB') {
                $result = \WxPayApi::unifiedOrder($config, $input);
                $url = isset($result['mweb_url']) ? $result["mweb_url"] : '';
                if ($url) {
                    $url .= '&redirect_url=' . urlencode($this->get_checkout_return_url($order_id));
                    wp_redirect($url);
                    exit;
                }
            } else if ($trade_type == 'NATIVE') {
                $result = \WxPayApi::unifiedOrder($config, $input);
                $url = isset($result['code_url']) ? $result["code_url"] : '';
                
                // 记录二维码URL到日志
                self::add_log('#' . $order_id . '生成支付二维码URL: ' . $url, $this->id, $order_id);
                
                // 如果URL为空，记录错误信息
                if(empty($url)) {
                    self::add_log('#' . $order_id . '错误：未能获取到二维码URL。返回结果: ' . wp_json_encode($result), $this->id, $order_id);
                }
            } else if ($trade_type == 'JSAPI') {
                $result = \WxPayApi::unifiedOrder($config, $input);
                $jsApiParameters = $tools->GetJsApiParameters($result);
            }
            $error_msg = '';
            if ((isset($result['result_code']) && $result['result_code'] == 'FAIL') || (isset($result['return_code']) && $result['return_code'] == 'FAIL')) {
                $error_msg =  "返回信息：" . $result['return_msg'] . " ；错误说明：" . (isset($result['err_code_des']) ? $result['err_code_des'] : '');
                // 记录错误信息到日志
                self::add_log('#' . $order_id . '微信支付错误: ' . $error_msg, $this->id, $order_id);
            }
        } catch (\Exception $e) {
            if($this->update_order_number($order_id, $result)) return false;
            $error_msg = '';
            if ((isset($result['result_code']) && $result['result_code'] == 'FAIL') || (isset($result['return_code']) && $result['return_code'] == 'FAIL')) {
                $error_msg =  "返回信息：" . $result['return_msg'] . " ；错误说明：" . $result['err_code_des'];
            }
            $error_msg = $error_msg ? $error_msg : $e->getMessage();
            echo '<div class="text-center" style="margin-bottom: 30px;color:#f66;">' . $error_msg . '</div>';
            return;
        }
        if($this->update_order_number($order_id, $result)) return false;
        ?>
        <?php if ($error_msg) { ?>
            <div class="text-center" style="margin-bottom: 30px;color:#f66;">
                <span style="color:red;"><?php echo $error_msg ?></span>
            </div>
        <?php } else {
            if ($trade_type == 'NATIVE') { ?>
                <div class="fd-pay-inner">
                    <div id="j-fd-wxpay" class="fd-pay-img" data-url="<?php echo esc_attr($url); ?>" data-order="<?php echo esc_attr($order_id); ?>"></div>
                    <p class="fd-pay-note" style="line-height:2;font-size: 16px;color: #333;">使用微信扫描二维码进行支付</p>
                    
                    <?php if(empty($url)): ?>
                    <div style="color:red;margin-top:10px;">错误：未能生成支付二维码，请刷新页面重试或联系管理员。</div>
                    <?php endif; ?>
                    
                    <div style="margin-top:15px;text-align:center;">
                        <button onclick="location.reload()" class="button">刷新页面</button>
                    </div>
                </div>
            <?php } else if ($trade_type == 'JSAPI') { ?>
                <script type="text/javascript">
                    window.FD_WXPay_jsApiParameters = <?php echo $jsApiParameters; ?>;
                    window.onload = function(){FD_WXPay_callpay();}
                </script>
                <div class="text-center" style="margin-bottom: 30px;padding: 15px 0;">
                    <button id="j-fd-wxpay2" data-order="<?php echo $order_id; ?>" style="width:100%;height:48px;border-radius: 5px;background-color:#44b549; border:0px;color:#fff;font-size:16px;line-height: 48px;padding: 0;" type="button" onclick="FD_WXPay_callpay();">立即支付</button>
                </div>
        <?php }
        } ?>
        <script src="<?php echo includes_url('js/jquery/jquery.min.js'); ?>"></script>
        <script src="<?php echo plugin_dir_url(dirname(__FILE__)) . 'assets/js/pay.js'; ?>"></script>
        <script> _ajax_url = "<?php echo admin_url( 'admin-ajax.php');?>"; _nonce = "<?php echo wp_create_nonce('fd_wxpay_receipt_page');?>"; </script>
    <?php }

    public function check_response($order_id) {
        if(!is_numeric($order_id)) return;
        
        self::add_log('#'.$order_id.' 微信异步通知开始', $this->id, $order_id);
        $xml = file_get_contents('php://input');
        if(empty($xml)) exit;

        //排除非微信回调
        if(strpos($xml, 'transaction_id')===false
            ||strpos($xml, 'appid')===false
            ||strpos($xml, 'mch_id')===false) exit;

        require_once dirname(__FILE__) . '/wxpay/WxPay.Config.php';
        include_once dirname(__FILE__) . '/wxpay/notify.php';

        // 如果返回成功则验证签名
        try {
            $config = new \WeChat_Config($this->_config);
            $notify = new \PayNotifyCallBack();
            $result = $notify->Handle($config, false);

            $values = $notify->FromXml($xml);

            self::add_log('#'.$values['out_trade_no'].' 微信异步通知数据：' . wp_json_encode($values), $this->id, $order_id);
            if($result && $values['return_code'] === 'SUCCESS' && $values['result_code'] === 'SUCCESS') {
                self::add_log('#'.$values['out_trade_no'].' 微信异步通知验证成功：' . $values['result_code'] .'；微信订单：' . $values['transaction_id'], $this->id, $order_id);
                
                // 获取订单信息并更新状态
                $order = fd_payment_get_order($order_id);
                if($order && $order->order_number && $order->order_number == $values['out_trade_no'] && $order->payment_status === 'unpaid'){ // 判断订单号，以及支付状态
                    fd_payment_complete_order($order->id, $this->id);
                    self::add_log('#'.$values['out_trade_no'].' 订单支付完成！', $this->id, $order_id);
                }
            }
            exit;
        } catch (\Exception $e) {
            exit;
        }
    }

    function update_order_number($order_id, $result) {
        // 检查是否订单号重复，如重复则更新订单号重新支付
        if(isset($result['err_code']) && $result['err_code'] === 'OUT_TRADE_NO_USED'){
            // 更新订单号
            $order = fd_payment_get_order($order_id);
            if($order && $order->payment_status === 'unpaid'){
                // TODO: 更新订单号逻辑
                return true;
            }
        }
        return false;
    }

    function is_weixin() {
        if (strpos($_SERVER['HTTP_USER_AGENT'], 'MicroMessenger') !== false) {
            return true;
        }
        return false;
    }

    public static function add_log($log, $type, $order_id = 0) {
        // 调用新的日志系统
        fd_payment_log($log, $type, 'info', $order_id);
    }

    public function return_page($order_id) {
        $order = fd_payment_get_order($order_id);
        
        $target = isset($this->modal) && $this->modal ? 'target="_parent"' : '';
        if ($order && $order->payment_status === 'paid') {
            // 获取前端域名配置
            $frontend_url = get_option('fd_frontend_url', home_url());
            // 确保URL末尾没有斜杠
            $frontend_url = rtrim($frontend_url, '/');

            // 检查是否是会员等级购买
            if ($order->product_type === 'member_level') {
                // 构建基础重定向URL
                $redirect_url = $frontend_url . '/membership/payment-success';

                // 添加调试日志
                self::add_log('#微信支付成功，会员升级订单，订单ID: ' . $order_id, $this->id, $order_id);
                self::add_log('#订单metadata: ' . $order->metadata, $this->id, $order_id);

                // 尝试解析metadata获取returnUrl
                $return_url = null;
                if (!empty($order->metadata)) {
                    $metadata = json_decode($order->metadata, true);
                    if (is_array($metadata) && isset($metadata['returnUrl']) && !empty($metadata['returnUrl'])) {
                        $return_url = $metadata['returnUrl'];
                        self::add_log('#从metadata中提取到returnUrl: ' . $return_url, $this->id, $order_id);
                    }
                }

                // 如果有returnUrl，添加到重定向URL中
                if ($return_url) {
                    $redirect_url .= '?returnUrl=' . urlencode($return_url);
                    self::add_log('#最终重定向URL: ' . $redirect_url, $this->id, $order_id);
                } else {
                    self::add_log('#未找到returnUrl，使用默认重定向URL: ' . $redirect_url, $this->id, $order_id);
                }
                ?>
                <script type="text/javascript">
                    // 使用JavaScript跳转，确保在iframe内也能正确跳转
                    window.top.location.href = "<?php echo esc_url($redirect_url); ?>";
                </script>
                <?php
                // 添加备用HTML跳转
                echo '<div class="order-status order-status-success">';
                echo '<div class="order-status-icon"></div>';
                echo '<p>订单支付成功，正在跳转...</p>';
                echo '<p>如果没有自动跳转，请 <a href="' . esc_url($redirect_url) . '">点击这里</a></p>';
                echo '</div>';
                // 立即结束输出
                exit();
            }

            // 检查是否是文章解锁订单
            if ($order->product_type === 'post_unlock') {
                // 构建文章解锁成功页面URL
                $redirect_url = $frontend_url . '/payment/post-unlock-success';

                // 添加调试日志
                self::add_log('#微信支付成功，文章解锁订单，订单ID: ' . $order_id, $this->id, $order_id);
                self::add_log('#订单metadata: ' . $order->metadata, $this->id, $order_id);

                // 尝试解析metadata获取callbackUrl（文章页面URL）
                $callback_url = null;
                if (!empty($order->metadata)) {
                    $metadata = json_decode($order->metadata, true);
                    if (is_array($metadata) && isset($metadata['callbackUrl']) && !empty($metadata['callbackUrl'])) {
                        $callback_url = $metadata['callbackUrl'];
                        self::add_log('#从metadata中提取到callbackUrl: ' . $callback_url, $this->id, $order_id);
                    }
                }

                // 如果有callbackUrl，添加到重定向URL中
                if ($callback_url) {
                    $redirect_url .= '?returnUrl=' . urlencode($callback_url);
                    self::add_log('#最终重定向URL: ' . $redirect_url, $this->id, $order_id);
                } else {
                    self::add_log('#未找到callbackUrl，使用默认重定向URL: ' . $redirect_url, $this->id, $order_id);
                }
                ?>
                <script type="text/javascript">
                    // 使用JavaScript跳转，确保在iframe内也能正确跳转
                    window.top.location.href = "<?php echo esc_url($redirect_url); ?>";
                </script>
                <?php
                // 添加备用HTML跳转
                echo '<div class="order-status order-status-success">';
                echo '<div class="order-status-icon"></div>';
                echo '<p>支付成功！您已解锁文章，正在跳转回到文章页...</p>';
                echo '<p>如果没有自动跳转，请 <a href="' . esc_url($redirect_url) . '">点击这里</a></p>';
                echo '</div>';
                // 立即结束输出
                exit();
            }
            ?>
            <div class="order-status order-status-success">
                <div class="order-status-icon"></div>
                <p>订单支付成功</p>
                <p>订单号：<?php echo esc_html($order->order_number); ?></p>
                <p>商品名称：<?php echo esc_html($order->title); ?></p>
                <p>支付金额：￥<?php echo number_format($order->amount, 2); ?></p>
                <p style="margin: 15px 0;"><a href="<?php echo home_url(); ?>" class="btn">返回网站</a></p>
            </div>
        <?php } else { ?>
            <div class="order-status order-status-fail" style="padding: 30px 15px;">
                <div class="order-status-icon"></div>
                <p>订单暂未完成支付</p>
                <p style="margin-top: 2px;">如有疑问请联系网站客服获取帮助</p>
                <a class="btn btn-help" href="javascript:window.location.reload();">已支付？刷新试试</a>
            </div>
        <?php }
    }
}

new Wxpay(); 